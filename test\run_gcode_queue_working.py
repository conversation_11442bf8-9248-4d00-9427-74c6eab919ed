#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 G-code队列执行 - 基于成功的队列模式
"""

import sys
import os
import time
import math
import re

# 添加lib目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))
import nrc_interface as nrc

# 配置参数
ROBOT_IP = "************"
ROBOT_PORT = 6001
GCODE_FILE = "jiyi.Gcode"

# 运动参数
VELOCITY_PERCENT = 30.0
ACCEL_PERCENT = 30.0

# G-code角度默认值和偏移
GCODE_DEFAULT_A = 0.0
GCODE_DEFAULT_B = 0.0
GCODE_DEFAULT_C = 0.0

GCODE_TO_ROBOT_OFFSET_A = 180.0
GCODE_TO_ROBOT_OFFSET_B = 0.0
GCODE_TO_ROBOT_OFFSET_C = -90.0

# 全局变量
Size = 0

def parse_gcode_file(filename):
    """解析G-code文件，提取路径点"""
    path_points = []
    
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                line = line.strip()
                if not line or line.startswith(';'):
                    continue
                
                # 匹配G1指令
                if line.startswith('G1'):
                    # 提取坐标值
                    x = re.search(r'X([-+]?\d*\.?\d+)', line)
                    y = re.search(r'Y([-+]?\d*\.?\d+)', line)
                    z = re.search(r'Z([-+]?\d*\.?\d+)', line)
                    a = re.search(r'A([-+]?\d*\.?\d+)', line)
                    b = re.search(r'B([-+]?\d*\.?\d+)', line)
                    c = re.search(r'C([-+]?\d*\.?\d+)', line)
                    
                    if x and y and z:
                        point = {
                            'x': float(x.group(1)),
                            'y': float(y.group(1)),
                            'z': float(z.group(1)),
                            'a': float(a.group(1)) if a else None,
                            'b': float(b.group(1)) if b else None,
                            'c': float(c.group(1)) if c else None,
                            'line': line_num
                        }
                        path_points.append(point)
                            
    except FileNotFoundError:
        print(f"❌ 找不到G-code文件: {filename}")
        return None
    except Exception as e:
        print(f"❌ 解析G-code文件时发生错误: {e}")
        return None
    
    return path_points

def connect(ip, port):
    fd = nrc.connect_robot(ip, port)
    print("初始化控制器ID: ", fd)
    return fd

def clear_error(socketFd):
    nrc.clear_error(socketFd)
    print('清错')

def power_on(socketFd):
    nrc.set_servo_poweron(socketFd)
    print('上使能')

def create_gcode_move_cmd(point):
    """基于成功模式创建G-code运动命令"""
    # 计算角度
    gcode_a = point['a'] if point['a'] is not None else GCODE_DEFAULT_A
    gcode_b = point['b'] if point['b'] is not None else GCODE_DEFAULT_B
    gcode_c = point['c'] if point['c'] is not None else GCODE_DEFAULT_C

    rx_deg = gcode_a + GCODE_TO_ROBOT_OFFSET_A
    ry_deg = gcode_b + GCODE_TO_ROBOT_OFFSET_B
    rz_deg = gcode_c + GCODE_TO_ROBOT_OFFSET_C

    # 创建位置向量
    pos = nrc.VectorDouble()
    target_pos = [
        point['x'], point['y'], point['z'],
        math.radians(rx_deg), math.radians(ry_deg), math.radians(rz_deg)
    ]
    
    for value in target_pos:
        pos.append(value)

    # 创建运动命令（按照成功的模式）
    cmd = nrc.MoveCmd()
    cmd.targetPosType = nrc.PosType_data
    cmd.targetPosValue = pos
    cmd.velocity = VELOCITY_PERCENT
    cmd.acc = ACCEL_PERCENT
    cmd.dec = ACCEL_PERCENT
    
    return cmd

def queue_gcode_execution(socketFd, gcode_points):
    """基于成功模式的G-code队列执行"""
    global Size
    Size = 0
    
    print(f"\n🚀 开始队列模式执行 {len(gcode_points)} 个G-code点...")
    
    # 1. 启动队列模式
    print("=== 启动队列模式 ===")
    result = nrc.queue_motion_set_status(socketFd, True)
    print(f"启动队列模式结果: {result}")
    if result != 0:
        return False
    time.sleep(1)
    
    # 2. 分批处理（每批最多20个）
    batch_size = 20
    total_points = len(gcode_points)
    
    for batch_start in range(0, total_points, batch_size):
        batch_end = min(batch_start + batch_size, total_points)
        batch_points = gcode_points[batch_start:batch_end]
        
        print(f"\n=== 处理第 {batch_start//batch_size + 1} 批：{batch_start+1}-{batch_end} ({len(batch_points)} 个指令) ===")
        
        # 清除队列（为新批次准备）
        if batch_start > 0:
            result = nrc.queue_motion_clear_Data(socketFd)
            if result != 0:
                print(f"❌ 清除队列失败，错误码: {result}")
                return False
        
        # 添加当前批次的指令
        batch_size_counter = 0
        for i, point in enumerate(batch_points):
            cmd = create_gcode_move_cmd(point)
            print(f"添加第 {batch_start + i + 1} 个指令: X={point['x']:.1f}, Y={point['y']:.1f}, Z={point['z']:.1f}")
            
            # 使用moveL进行直线运动
            result = nrc.queue_motion_push_back_moveL(socketFd, cmd)
            if result == 0:
                batch_size_counter += 1
                print(f"✅ 成功添加，当前批次Size: {batch_size_counter}")
            else:
                print(f"❌ 指令插入失败，错误码: {result}")
                return False
        
        # 发送当前批次并执行
        print(f"\n=== 发送第 {batch_start//batch_size + 1} 批队列并执行 ===")
        result = nrc.queue_motion_send_to_controller(socketFd, batch_size_counter)
        print(f"运动队列长度: {batch_size_counter}")
        print(f"发送队列结果: {result}")
        if result != 0:
            print(f"❌ 发送队列失败")
            return False
        
        time.sleep(0.5)
        
        # 检查队列长度
        queue_len = 0
        result = nrc.queue_motion_get_queuelen(socketFd, queue_len)
        if isinstance(result, list) and len(result) > 1:
            queue_len = result[1]
        print(f"发送后队列长度: {queue_len}")
        
        if queue_len == 0:
            print(f"❌ 警告：队列长度为0")
            return False
        
        # 等待当前批次执行完成
        print(f"⏳ 等待第 {batch_start//batch_size + 1} 批执行完成...")
        jug = 1
        while jug != 0:
            status = 1
            status = nrc.get_robot_running_state(socketFd, status) 
            jug = status[1]
            if jug == 2:
                print(f"   🔄 机器人正在运动...")
            elif jug == 0:
                print(f"   ✅ 第 {batch_start//batch_size + 1} 批执行完成")
            time.sleep(0.5)
    
    print(f"\n🎉 所有G-code点执行完成！")
    return True

def main():
    print("=" * 60)
    print("INEXBOT机械臂 G-code队列执行 - 基于成功模式")
    print("=" * 60)
    
    # 1. 解析G-code文件
    print(f"📄 正在解析G-code文件: {GCODE_FILE}")
    gcode_path = parse_gcode_file(GCODE_FILE)
    if not gcode_path:
        return
    
    print(f"✅ 解析完成，共找到 {len(gcode_path)} 个路径点。")
    
    # 2. 连接机械臂
    print(f"\n🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
    socketFd = connect(ROBOT_IP, str(ROBOT_PORT))
    if socketFd <= 0:
        print(f"❌ 连接失败，Socket ID: {socketFd}")
        return
    
    try:
        # 3. 清错和上电
        clear_error(socketFd)
        time.sleep(0.5)
        power_on(socketFd)
        time.sleep(2)
        
        # 4. 设置远程模式
        print("设置远程模式...")
        result = nrc.set_current_mode(socketFd, 1)  # 1 = 远程模式
        print(f"设置远程模式结果: {result}")
        time.sleep(0.5)
        
        # 5. 执行G-code队列
        success = queue_gcode_execution(socketFd, gcode_path)
        if success:
            print(f"\n🎉 G-code路径执行成功！")
        else:
            print(f"\n❌ G-code路径执行失败")
        
    except Exception as e:
        print(f"❌ 执行过程中发生错误: {e}")
        
    finally:
        # 清理
        try:
            print("\n=== 清理队列模式 ===")
            nrc.queue_motion_set_status(socketFd, False)
            nrc.set_current_mode(socketFd, 0)  # 0 = 示教模式
            print("队列模式已关闭")
        except Exception as e:
            print(f"清理时发生错误: {e}")
        
        # 下电和断开
        print("\n=== 下电和断开 ===")
        nrc.set_servo_poweroff(socketFd)
        time.sleep(1)
        nrc.disconnect_robot(socketFd)
        print("已断开连接")

if __name__ == "__main__":
    main()
