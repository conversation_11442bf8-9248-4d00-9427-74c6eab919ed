#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 G-code 智能执行程序
先移动到起始位置，再执行G-code路径
"""

import sys
import os
import time
import math
import re

# 添加lib目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))
import nrc_interface as nrc

# 配置参数
ROBOT_IP = "************"
ROBOT_PORT = 6001
GCODE_FILE = "jiyi.Gcode"
MAX_POINTS = 500  # 先测试50个点

# 运动参数
USER_COORD_NUMBER = 1
VELOCITY_PERCENT = 30.0  # 降低速度以确保安全
ACCEL_PERCENT = 30.0
SMOOTHING_LEVEL = 0

# G-code角度默认值和偏移
GCODE_DEFAULT_A = 0.0
GCODE_DEFAULT_B = 0.0
GCODE_DEFAULT_C = 0.0

GCODE_TO_ROBOT_OFFSET_A = 180.0
GCODE_TO_ROBOT_OFFSET_B = 0.0
GCODE_TO_ROBOT_OFFSET_C = -90.0

def parse_gcode_file(filename, max_points=None):
    """解析G-code文件，提取路径点"""
    path_points = []
    
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                line = line.strip()
                if not line or line.startswith(';'):
                    continue
                
                # 匹配G1指令
                if line.startswith('G1'):
                    # 提取坐标值
                    x = re.search(r'X([-+]?\d*\.?\d+)', line)
                    y = re.search(r'Y([-+]?\d*\.?\d+)', line)
                    z = re.search(r'Z([-+]?\d*\.?\d+)', line)
                    a = re.search(r'A([-+]?\d*\.?\d+)', line)
                    b = re.search(r'B([-+]?\d*\.?\d+)', line)
                    c = re.search(r'C([-+]?\d*\.?\d+)', line)
                    
                    if x and y and z:
                        point = {
                            'x': float(x.group(1)),
                            'y': float(y.group(1)),
                            'z': float(z.group(1)),
                            'a': float(a.group(1)) if a else None,
                            'b': float(b.group(1)) if b else None,
                            'c': float(c.group(1)) if c else None,
                            'line': line_num
                        }
                        path_points.append(point)
                        
                        if max_points and len(path_points) >= max_points:
                            break
                            
    except FileNotFoundError:
        print(f"❌ 找不到G-code文件: {filename}")
        return None
    except Exception as e:
        print(f"❌ 解析G-code文件时发生错误: {e}")
        return None
    
    return path_points

def get_current_position(socket_fd):
    """获取当前位置（用户坐标系）"""
    try:
        pos = nrc.VectorDouble()
        for i in range(7):
            pos.append(0.0)
        
        result = nrc.get_current_position(socket_fd, 3, pos)  # 3 = 用户坐标系
        if result == 0 and len(pos) >= 6:
            return [pos[i] for i in range(6)]
        else:
            return None
    except Exception as e:
        print(f"❌ 获取当前位置时发生错误: {e}")
        return None

def move_to_position(socket_fd, target_pos, description="目标位置"):
    """移动到指定位置"""
    print(f"🎯 移动到{description}...")
    print(f"   目标: X={target_pos[0]:.1f}, Y={target_pos[1]:.1f}, Z={target_pos[2]:.1f}")
    print(f"   姿态: RX={math.degrees(target_pos[3]):.1f}°, RY={math.degrees(target_pos[4]):.1f}°, RZ={math.degrees(target_pos[5]):.1f}°")
    
    # 创建运动命令
    move_cmd = nrc.MoveCmd()
    move_cmd.targetPosType = 0
    move_cmd.targetPosValue = nrc.VectorDouble()
    
    for val in target_pos:
        move_cmd.targetPosValue.append(val)
    
    move_cmd.coord = 3  # 用户坐标系
    move_cmd.userNum = USER_COORD_NUMBER
    move_cmd.velocity = VELOCITY_PERCENT
    move_cmd.acc = ACCEL_PERCENT
    move_cmd.dec = ACCEL_PERCENT
    move_cmd.pl = SMOOTHING_LEVEL
    
    # 执行移动
    result = nrc.robot_movel(socket_fd, move_cmd)
    if result != 0:
        print(f"❌ 移动失败，错误码: {result}")
        return False
    
    # 等待移动完成
    print(f"⏳ 等待移动完成...")
    time.sleep(2)  # 给足够时间移动
    
    # 检查是否到达
    current_pos = get_current_position(socket_fd)
    if current_pos:
        distance = math.sqrt(
            (current_pos[0] - target_pos[0])**2 + 
            (current_pos[1] - target_pos[1])**2 + 
            (current_pos[2] - target_pos[2])**2
        )
        print(f"✅ 移动完成，距离目标: {distance:.1f}mm")
        return True
    else:
        print(f"⚠️ 无法确认移动结果")
        return True

def get_queue_length(socket_fd):
    """获取当前队列长度"""
    try:
        queue_len = 0
        result = nrc.queue_motion_get_queuelen(socket_fd, queue_len)

        if isinstance(result, list) and len(result) > 1:
            return result[1]
        else:
            return 0
    except Exception as e:
        print(f"⚠️ 获取队列长度时发生错误: {e}")
        return 0

def execute_points_individually(socket_fd, gcode_points):
    """逐点执行G-code路径（备用方案）"""
    print(f"📝 使用逐点执行模式处理 {len(gcode_points)} 个点...")

    start_time = time.time()

    for i, point in enumerate(gcode_points):
        print(f"📍 执行第 {i+1}/{len(gcode_points)} 个点...")

        # 计算角度
        gcode_a = point['a'] if point['a'] is not None else GCODE_DEFAULT_A
        gcode_b = point['b'] if point['b'] is not None else GCODE_DEFAULT_B
        gcode_c = point['c'] if point['c'] is not None else GCODE_DEFAULT_C

        rx_deg = gcode_a + GCODE_TO_ROBOT_OFFSET_A
        ry_deg = gcode_b + GCODE_TO_ROBOT_OFFSET_B
        rz_deg = gcode_c + GCODE_TO_ROBOT_OFFSET_C

        # 设置目标位置
        target_pos = [
            point['x'], point['y'], point['z'],
            math.radians(rx_deg), math.radians(ry_deg), math.radians(rz_deg)
        ]

        # 创建运动命令
        move_cmd = nrc.MoveCmd()
        move_cmd.targetPosType = 0
        move_cmd.targetPosValue = nrc.VectorDouble()

        for val in target_pos:
            move_cmd.targetPosValue.append(val)

        move_cmd.coord = 3  # 用户坐标系
        move_cmd.userNum = USER_COORD_NUMBER
        move_cmd.velocity = VELOCITY_PERCENT
        move_cmd.acc = ACCEL_PERCENT
        move_cmd.dec = ACCEL_PERCENT
        move_cmd.pl = SMOOTHING_LEVEL

        # 执行移动
        result = nrc.robot_movel(socket_fd, move_cmd)
        if result != 0:
            print(f"❌ 移动到第 {i+1} 个点失败，错误码: {result}")
            return False

        # 简单等待（不做精确位置检查以提高速度）
        time.sleep(0.5)

    elapsed = time.time() - start_time
    print(f"✅ 逐点执行完成！总用时: {elapsed:.1f}s")
    return True

def execute_gcode_queue(socket_fd, gcode_points):
    """使用队列模式执行G-code路径"""
    print(f"\n🚀 尝试使用队列模式执行剩余 {len(gcode_points)} 个G-code点...")
    print(f"⚠️ 如果队列模式失败，将回退到逐点执行模式")

    # 首先尝试队列模式
    queue_success = try_queue_execution(socket_fd, gcode_points)

    if not queue_success:
        print(f"\n🔄 队列模式失败，切换到逐点执行模式...")
        return execute_points_individually(socket_fd, gcode_points)

    return True

def try_queue_execution(socket_fd, gcode_points):
    """尝试队列模式执行"""
    try:
        # 1. 停止任何现有的队列运动
        print(f"🛑 停止现有队列运动...")
        nrc.queue_motion_stop(socket_fd)
        time.sleep(0.5)

        # 2. 初始化队列模式
        print(f"🔧 正在初始化队列模式...")
        result = nrc.queue_motion_set_status(socket_fd, True)
        if result != 0:
            print(f"❌ 启用队列模式失败，错误码: {result}")
            return False

        result = nrc.queue_motion_clear_Data(socket_fd)
        if result != 0:
            print(f"❌ 清除队列数据失败，错误码: {result}")
            return False

        print(f"✅ 队列模式已启用")

        # 2. 分批添加指令到队列（尝试小批量处理）
        print(f"📝 分批添加 {len(gcode_points)} 个指令到队列...")
        points_added = 0
        batch_size = 10  # 每批10个指令

        for batch_start in range(0, len(gcode_points), batch_size):
            batch_end = min(batch_start + batch_size, len(gcode_points))
            batch_points = gcode_points[batch_start:batch_end]

            print(f"   正在添加第 {batch_start+1}-{batch_end} 个指令...")

            for i, point in enumerate(batch_points):
                # 计算角度
                gcode_a = point['a'] if point['a'] is not None else GCODE_DEFAULT_A
                gcode_b = point['b'] if point['b'] is not None else GCODE_DEFAULT_B
                gcode_c = point['c'] if point['c'] is not None else GCODE_DEFAULT_C

                rx_deg = gcode_a + GCODE_TO_ROBOT_OFFSET_A
                ry_deg = gcode_b + GCODE_TO_ROBOT_OFFSET_B
                rz_deg = gcode_c + GCODE_TO_ROBOT_OFFSET_C

                # 创建运动命令
                move_cmd = nrc.MoveCmd()
                move_cmd.targetPosType = 0
                move_cmd.targetPosValue = nrc.VectorDouble()

                # 设置目标位置
                target_pos = [
                    point['x'], point['y'], point['z'],
                    math.radians(rx_deg), math.radians(ry_deg), math.radians(rz_deg)
                ]

                for val in target_pos:
                    move_cmd.targetPosValue.append(val)

                move_cmd.coord = 3  # 用户坐标系
                move_cmd.userNum = USER_COORD_NUMBER
                move_cmd.velocity = VELOCITY_PERCENT
                move_cmd.acc = ACCEL_PERCENT
                move_cmd.dec = ACCEL_PERCENT
                move_cmd.pl = SMOOTHING_LEVEL

                # 添加到队列
                result = nrc.queue_motion_push_back_moveL(socket_fd, move_cmd)
                if result != 0:
                    print(f"❌ 添加第 {batch_start + i + 1} 个指令到队列失败，错误码: {result}")
                    break

                points_added += 1

            # 检查当前队列长度
            current_queue_len = get_queue_length(socket_fd)
            print(f"   当前队列长度: {current_queue_len}")

        print(f"✅ 成功添加了 {points_added} 个指令到队列")

        # 3. 检查队列状态（不发送到控制器，让队列自动执行）
        print(f"� 检查队列状态...")
        initial_queue_len = get_queue_length(socket_fd)
        print(f"📊 添加后队列长度: {initial_queue_len}")

        if initial_queue_len == 0:
            print(f"⚠️ 警告：队列长度为0，尝试发送到控制器...")

            # 尝试发送到控制器
            result = nrc.queue_motion_send_to_controller(socket_fd, points_added)
            if result != 0:
                print(f"❌ 发送队列到控制器失败，错误码: {result}")
                return False

            print(f"✅ 队列已发送到控制器！")

            # 再次检查队列长度
            time.sleep(1.0)
            initial_queue_len = get_queue_length(socket_fd)
            print(f"📊 发送后队列长度: {initial_queue_len}")

            if initial_queue_len == 0:
                print(f"❌ 队列仍然为空，队列运动可能不支持或配置错误")
                return False

        print(f"✅ 队列运动准备就绪，开始执行！")

        # 4. 监控执行状态
        print(f"📊 监控执行状态...")
        start_time = time.time()
        last_queue_len = -1

        # 等待一小段时间让队列开始执行
        time.sleep(1.0)

        while True:
            current_queue_len = get_queue_length(socket_fd)

            if current_queue_len != last_queue_len:
                elapsed = time.time() - start_time
                completed = points_added - current_queue_len
                print(f"   队列长度: {current_queue_len}, 已完成: {completed}/{points_added}, 用时: {elapsed:.1f}s")
                last_queue_len = current_queue_len

            if current_queue_len == 0:
                elapsed = time.time() - start_time
                print(f"🎉 G-code路径执行完成！总用时: {elapsed:.1f}s")
                break

            time.sleep(1.0)  # 增加监控间隔，减少查询频率

        return True

    except Exception as e:
        print(f"❌ 队列模式执行时发生错误: {e}")
        return False

    finally:
        # 清理队列模式
        try:
            print(f"🛑 正在停止队列运动...")
            nrc.queue_motion_stop(socket_fd)

            print(f"🔧 正在禁用队列模式...")
            nrc.queue_motion_set_status(socket_fd, False)
            print(f"✅ 队列模式已安全关闭")
        except Exception as e:
            print(f"⚠️ 清理队列模式时发生错误: {e}")

def execute_gcode_smart():
    """智能执行G-code"""
    
    print("=" * 60)
    print("INEXBOT机械臂 G-code 智能执行程序")
    print(f"测试前 {MAX_POINTS} 个指令")
    print("=" * 60)
    
    # 1. 解析G-code文件
    print(f"📄 正在解析G-code文件: {GCODE_FILE}")
    gcode_path = parse_gcode_file(GCODE_FILE, MAX_POINTS)
    if not gcode_path:
        return
    
    print(f"✅ 解析完成，共找到 {len(gcode_path)} 个路径点。")
    
    # 显示G-code范围
    x_coords = [p['x'] for p in gcode_path]
    y_coords = [p['y'] for p in gcode_path]
    z_coords = [p['z'] for p in gcode_path]
    
    print(f"📊 G-code坐标范围:")
    print(f"   X: {min(x_coords):.1f} ~ {max(x_coords):.1f}")
    print(f"   Y: {min(y_coords):.1f} ~ {max(y_coords):.1f}")
    print(f"   Z: {min(z_coords):.1f} ~ {max(z_coords):.1f}")
    
    # 2. 连接机械臂
    print(f"\n🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
    socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
    if socket_fd <= 0:
        print(f"❌ 连接失败，Socket ID: {socket_fd}")
        return
    
    print(f"✅ 连接成功！Socket ID: {socket_fd}")
    
    try:
        # 3. 机器人上电
        print(f"\nℹ️ 机器人需要上电，开始上电流程...")
        nrc.clear_error(socket_fd)
        time.sleep(0.2)
        result = nrc.set_servo_poweron(socket_fd)
        if result != 0:
            print(f"❌ 机器人上电失败，错误码: {result}")
            return
        time.sleep(1.5)
        print(f"✅ 机器人上电成功！")
        
        # 4. 设置用户坐标系
        print(f"ℹ️ 设置当前用户坐标系为: {USER_COORD_NUMBER}")
        result = nrc.set_user_coord_number(socket_fd, USER_COORD_NUMBER)
        if result != 0:
            print(f"❌ 设置用户坐标系失败，错误码: {result}")
            return
        
        # 5. 获取当前位置
        print(f"\n🔍 获取当前机械臂位置...")
        current_pos = get_current_position(socket_fd)
        if not current_pos:
            print(f"❌ 无法获取当前位置")
            return
        
        print(f"📊 当前位置: X={current_pos[0]:.1f}, Y={current_pos[1]:.1f}, Z={current_pos[2]:.1f}")
        print(f"         姿态: RX={math.degrees(current_pos[3]):.1f}°, RY={math.degrees(current_pos[4]):.1f}°, RZ={math.degrees(current_pos[5]):.1f}°")
        
        # 6. 计算起始位置（G-code第一个点上方50mm）
        first_point = gcode_path[0]
        gcode_a = first_point['a'] if first_point['a'] is not None else GCODE_DEFAULT_A
        gcode_b = first_point['b'] if first_point['b'] is not None else GCODE_DEFAULT_B
        gcode_c = first_point['c'] if first_point['c'] is not None else GCODE_DEFAULT_C
        
        start_pos = [
            first_point['x'],
            first_point['y'], 
            first_point['z'] + 50.0,  # 上方50mm
            math.radians(gcode_a + GCODE_TO_ROBOT_OFFSET_A),
            math.radians(gcode_b + GCODE_TO_ROBOT_OFFSET_B),
            math.radians(gcode_c + GCODE_TO_ROBOT_OFFSET_C)
        ]
        
        # 7. 移动到起始位置
        if not move_to_position(socket_fd, start_pos, "G-code起始位置上方50mm"):
            return
        
        # 8. 移动到G-code第一个点
        first_target = [
            first_point['x'],
            first_point['y'], 
            first_point['z'],
            math.radians(gcode_a + GCODE_TO_ROBOT_OFFSET_A),
            math.radians(gcode_b + GCODE_TO_ROBOT_OFFSET_B),
            math.radians(gcode_c + GCODE_TO_ROBOT_OFFSET_C)
        ]
        
        if not move_to_position(socket_fd, first_target, "G-code第一个点"):
            return
        
        print(f"\n🎉 成功到达G-code起始位置！")
        print(f"💡 现在开始执行G-code路径...")

        # 9. 使用队列模式执行剩余路径
        if len(gcode_path) > 1:
            execute_gcode_queue(socket_fd, gcode_path[1:])  # 跳过第一个点，因为已经到达了
        
    finally:
        # 清理和断开连接
        print(f"\nℹ️ 正在安全下电...")
        nrc.set_servo_poweroff(socket_fd)
        time.sleep(1)
        print(f"✅ 机器人已下电。")
        
        print(f"🔌 正在断开连接...")
        nrc.disconnect_robot(socket_fd)
        print(f"✅ 连接已断开。")

if __name__ == "__main__":
    execute_gcode_smart()
