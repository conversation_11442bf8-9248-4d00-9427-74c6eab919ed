
import sys
import os
import time

sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'lib'))

import nrc_interface as nrc
import time


# version = nrc.get_library_version()
# print(f"NRC库版本: {version}")

# socket_fd = nrc.connect_robot("192.168.1.13", "6001")
# print(socket_fd)



# import math

# socketFd = nrc.connect_robot("192.168.1.13", "6001")

# coord = 1
# pos = nrc.VectorDouble()
# for i in range(7):
#     pos.append(0.0)
# result_code = nrc.get_current_position(socketFd, coord, pos)
# if result_code == 0:
#     print(f"\n✅ 获取位置成功！")
#     print(f"📍 当前位置: X={pos[0]:.3f}, Y={pos[1]:.3f}, Z={pos[2]:.3f}")
#     print(f"📍 当前姿态: RX={math.degrees(pos[3]):.3f}°, RY={math.degrees(pos[4]):.3f}°, RZ={math.degrees(pos[5]):.3f}°")
#     print(f"📍 当前姿态(弧度): RX={pos[3]:.3f}, RY={pos[4]:.3f}, RZ={pos[5]:.3f}")

# else:
#     print(f"\n❌ 获取位置失败，错误代码: {result_code}")
# nrc.disconnect_robot(socketFd)
# print("\n🔗 连接已断开")


# # 连接机器人
# socketFd = nrc.connect_robot("192.168.1.13", "6001")
# if socketFd <= 0:
#     print("❌ 连接失败")
#     exit()

# print("✅ 连接成功，准备上电并移动...")

# # --- 2. 上电 ---
# nrc.clear_error(socketFd)
# nrc.set_servo_state(socketFd, 1) # 1 = 就绪状态
# time.sleep(0.5)
# nrc.set_servo_poweron(socketFd)
# time.sleep(2)
# # 在连接并上电后，添加以下代码
# print("--- 检查用户坐标系1的参数 ---")
# user_coord_num = 1
# pos_vector = nrc.VectorDouble()
# for i in range(7):
#     pos_vector.append(0.0)

# # 调用函数获取参数
# result_code = nrc.get_user_coord_para(socketFd, user_coord_num, pos_vector)

# if result_code == 0:
#     user_coord_params = [pos_vector[i] for i in range(6)]
#     print(f"✅ 读取到用户坐标系 {user_coord_num} 的原点为:")
#     print(f"  X={user_coord_params[0]:.3f}, Y={user_coord_params[1]:.3f}, Z={user_coord_params[2]:.3f}")
#     print(f"  RX={user_coord_params[3]:.3f}, RY={user_coord_params[4]:.3f}, RZ={user_coord_params[5]:.3f}")
# else:
#     print(f"❌ 无法读取用户坐标系 {user_coord_num} 的参数，错误码: {result_code}")

# # 在这里暂停一下，让您有机会检查打印出的坐标是否合理
# input("请按回车键继续执行移动...")

# # 检查当前工具号
# tool_num = 0
# result = nrc.get_tool_hand_number(socketFd, tool_num)
# if isinstance(result, list):
#     print(f"当前激活的工具号: {result[1]}")


# G1 X84.4543 Y188.3451 Z2.0002 A38.811 B0.000 C0.609 F3000
# --- 1. 在这里设置你期望的目标位姿 (单位: mm 和 度) ---
target_x = 84.4543
target_y = 188.3451
target_z = 2.0002
target_rx_deg = 0.6774  
target_ry_deg = 0.0000
target_rz_deg = 0.0106  



# 连接机器人
socketFd = nrc.connect_robot("192.168.1.13", "6001")
if socketFd <= 0:
    print("❌ 连接失败")
    exit()

print("✅ 连接成功，准备上电并移动...")

# --- 2. 上电 ---
nrc.clear_error(socketFd)
nrc.set_servo_state(socketFd, 1) # 1 = 就绪状态
time.sleep(0.5)
nrc.set_servo_poweron(socketFd)
time.sleep(2)

# --- 3. 创建并发送运动指令 ---
move_cmd = nrc.MoveCmd()
move_cmd.targetPosType = 1      # 目标类型: 1=直角坐标
move_cmd.targetPosValue = nrc.VectorDouble()
target_pose = [target_x, target_y, target_z, target_rx_deg, target_ry_deg, target_rz_deg]
for val in target_pose:
    move_cmd.targetPosValue.append(val)

move_cmd.velocity = 80          # 速度
move_cmd.coord = 3              # 坐标系: 3=用户坐标系
move_cmd.pl=1
result_code = nrc.robot_movel(socketFd, move_cmd)

# --- 4. 验证指令是否成功发送 ---
if result_code == 0:
    print(f"✅ 指令发送成功, 机器人正在移动...")
    # 等待一个固定时间，让机器人有时间完成运动
    # 注意：这个时间需要根据实际运动距离和速度来调整
    print("⏳ 等待10秒...")
    time.sleep(10)
    print("✅ 运动时间结束")
else:
    print(f"❌ 发送运动指令失败，错误代码: {result_code}")

# --- 5. 下电并断开 ---
print("\n🔌 正在安全下电...")
nrc.set_servo_poweroff(socketFd)
nrc.disconnect_robot(socketFd)
print("🔗 连接已断开")


