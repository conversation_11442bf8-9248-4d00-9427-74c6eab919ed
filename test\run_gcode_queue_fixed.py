#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 G-code 队列执行程序 - 修复版
基于官方文档的正确队列模式实现
"""

import sys
import os
import time
import math
import re

# 添加lib目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))
import nrc_interface as nrc

# 配置参数
ROBOT_IP = "************"
ROBOT_PORT = 6001
GCODE_FILE = "jiyi.Gcode"
MAX_POINTS = 63  # 测试所有点

# 运动参数
USER_COORD_NUMBER = 1
VELOCITY_PERCENT = 30.0  # 降低速度以确保安全
ACCEL_PERCENT = 30.0
SMOOTHING_LEVEL = 0

# 执行模式配置
FORCE_INDIVIDUAL_MODE = False  # 设置为True直接使用逐点模式，跳过队列尝试
INDIVIDUAL_WAIT_TIME = 0.2     # 逐点执行时的等待时间（秒）

# G-code角度默认值和偏移
GCODE_DEFAULT_A = 0.0
GCODE_DEFAULT_B = 0.0
GCODE_DEFAULT_C = 0.0

GCODE_TO_ROBOT_OFFSET_A = 180.0
GCODE_TO_ROBOT_OFFSET_B = 0.0
GCODE_TO_ROBOT_OFFSET_C = -90.0

def parse_gcode_file(filename, max_points=None):
    """解析G-code文件，提取路径点"""
    path_points = []
    
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                line = line.strip()
                if not line or line.startswith(';'):
                    continue
                
                # 匹配G1指令
                if line.startswith('G1'):
                    # 提取坐标值
                    x = re.search(r'X([-+]?\d*\.?\d+)', line)
                    y = re.search(r'Y([-+]?\d*\.?\d+)', line)
                    z = re.search(r'Z([-+]?\d*\.?\d+)', line)
                    a = re.search(r'A([-+]?\d*\.?\d+)', line)
                    b = re.search(r'B([-+]?\d*\.?\d+)', line)
                    c = re.search(r'C([-+]?\d*\.?\d+)', line)
                    
                    if x and y and z:
                        point = {
                            'x': float(x.group(1)),
                            'y': float(y.group(1)),
                            'z': float(z.group(1)),
                            'a': float(a.group(1)) if a else None,
                            'b': float(b.group(1)) if b else None,
                            'c': float(c.group(1)) if c else None,
                            'line': line_num
                        }
                        path_points.append(point)
                        
                        if max_points and len(path_points) >= max_points:
                            break
                            
    except FileNotFoundError:
        print(f"❌ 找不到G-code文件: {filename}")
        return None
    except Exception as e:
        print(f"❌ 解析G-code文件时发生错误: {e}")
        return None
    
    return path_points

def get_current_position(socket_fd):
    """获取当前位置（用户坐标系）"""
    try:
        pos = nrc.VectorDouble()
        for _ in range(7):
            pos.append(0.0)
        
        result = nrc.get_current_position(socket_fd, 3, pos)  # 3 = 用户坐标系
        if result == 0 and len(pos) >= 6:
            return [pos[i] for i in range(6)]
        else:
            return None
    except Exception as e:
        print(f"❌ 获取当前位置时发生错误: {e}")
        return None

def servo_ready(socket_fd):
    """设置伺服就绪状态（按照官方文档）"""
    try:
        # 获取当前伺服状态
        servo_state = 0
        result = nrc.get_servo_state(socket_fd, servo_state)
        if isinstance(result, list) and len(result) > 1:
            servo_state = result[1]
        
        print(f"📊 当前伺服状态: {servo_state}")
        
        if servo_state == 0:
            # 伺服未就绪，设置为就绪
            print(f"🔧 设置伺服就绪...")
            result = nrc.set_servo_state(socket_fd, 1)
            if result != 0:
                print(f"❌ 设置伺服就绪失败，错误码: {result}")
                return False
        elif servo_state == 2:
            # 伺服错误状态，先清除错误再设置就绪
            print(f"🔧 清除伺服错误并设置就绪...")
            nrc.clear_error(socket_fd)
            time.sleep(0.2)
            result = nrc.set_servo_state(socket_fd, 1)
            if result != 0:
                print(f"❌ 设置伺服就绪失败，错误码: {result}")
                return False
        
        # 验证伺服状态
        time.sleep(0.5)
        result = nrc.get_servo_state(socket_fd, servo_state)
        if isinstance(result, list) and len(result) > 1:
            servo_state = result[1]
        
        print(f"✅ 伺服状态已设置为: {servo_state}")
        # 状态1=就绪，状态3=运行，都可以进行队列运动
        return servo_state in [1, 3]
        
    except Exception as e:
        print(f"❌ 设置伺服就绪时发生错误: {e}")
        return False

def wait_for_running_over(socket_fd):
    """等待机器人运动完成（按照官方文档）"""
    print(f"⏳ 等待队列运动完成...")
    try:
        while True:
            running_state = 0
            result = nrc.get_robot_running_state(socket_fd, running_state)
            if isinstance(result, list) and len(result) > 1:
                running_state = result[1]
            
            if running_state != 2:  # 2表示正在运动
                print(f"✅ 队列运动已完成，运动状态: {running_state}")
                break
            
            time.sleep(0.5)  # 每500ms检查一次
            
    except Exception as e:
        print(f"⚠️ 等待运动完成时发生错误: {e}")

def execute_queue_motion(socket_fd, gcode_points):
    """执行队列运动（按照官方文档的正确流程）"""
    try:
        print(f"\n🚀 使用队列模式执行 {len(gcode_points)} 个G-code点...")
        
        # 1. 设置伺服就绪
        print(f"🔧 检查并设置伺服就绪状态...")
        if not servo_ready(socket_fd):
            print(f"❌ 伺服未就绪，无法使用队列模式")
            return False

        # 2. 设置远程模式（队列运动的必要条件）
        print(f"🎮 设置机器人为远程模式...")
        result = nrc.set_current_mode(socket_fd, 1)  # 1 = 远程模式
        if result != 0:
            print(f"❌ 设置远程模式失败，错误码: {result}")
            return False
        time.sleep(0.5)
        print(f"✅ 机器人已设置为远程模式")

        # 3. 启用队列模式
        print(f"🔧 启用队列模式...")
        result = nrc.queue_motion_set_status(socket_fd, True)
        if result != 0:
            print(f"❌ 启用队列模式失败，错误码: {result}")
            return False
        
        # 等待队列模式启用
        time.sleep(1)
        
        # 3. 清除队列数据
        result = nrc.queue_motion_clear_Data(socket_fd)
        if result != 0:
            print(f"❌ 清除队列数据失败，错误码: {result}")
            return False
        
        print(f"✅ 队列模式已启用")
        
        # 4. 添加运动指令到队列
        print(f"📝 添加 {len(gcode_points)} 个指令到队列...")
        points_added = 0
        
        for i, point in enumerate(gcode_points):
            # 计算角度
            gcode_a = point['a'] if point['a'] is not None else GCODE_DEFAULT_A
            gcode_b = point['b'] if point['b'] is not None else GCODE_DEFAULT_B
            gcode_c = point['c'] if point['c'] is not None else GCODE_DEFAULT_C

            rx_deg = gcode_a + GCODE_TO_ROBOT_OFFSET_A
            ry_deg = gcode_b + GCODE_TO_ROBOT_OFFSET_B
            rz_deg = gcode_c + GCODE_TO_ROBOT_OFFSET_C

            # 创建运动命令
            move_cmd = nrc.MoveCmd()
            move_cmd.targetPosType = 0
            move_cmd.targetPosValue = nrc.VectorDouble()

            # 设置目标位置
            target_pos = [
                point['x'], point['y'], point['z'],
                math.radians(rx_deg), math.radians(ry_deg), math.radians(rz_deg)
            ]

            for val in target_pos:
                move_cmd.targetPosValue.append(val)

            move_cmd.coord = 3  # 用户坐标系
            move_cmd.userNum = USER_COORD_NUMBER
            move_cmd.velocity = VELOCITY_PERCENT
            move_cmd.acc = ACCEL_PERCENT
            move_cmd.dec = ACCEL_PERCENT
            move_cmd.pl = SMOOTHING_LEVEL

            # 添加到队列（使用moveS以获得更好的平滑性）
            result = nrc.queue_motion_push_back_moveS(socket_fd, move_cmd)
            if result != 0:
                print(f"❌ 添加第 {i+1} 个指令到队列失败，错误码: {result}")
                break

            points_added += 1
            
            # 每10个指令显示一次进度
            if (i + 1) % 10 == 0:
                print(f"   已添加 {i+1}/{len(gcode_points)} 个指令...")

        print(f"✅ 成功添加了 {points_added} 个指令到队列")
        
        # 5. 发送队列到控制器
        print(f"🚀 发送队列到控制器...")
        result = nrc.queue_motion_send_to_controller(socket_fd, points_added)
        if result != 0:
            print(f"❌ 发送队列到控制器失败，错误码: {result}")
            return False
        
        print(f"✅ 队列已发送到控制器！")
        print(f"🎯 开始执行队列运动...")
        
        # 6. 等待运动完成
        start_time = time.time()
        wait_for_running_over(socket_fd)
        
        elapsed = time.time() - start_time
        print(f"🎉 队列运动执行完成！总用时: {elapsed:.1f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ 队列模式执行时发生错误: {e}")
        return False
    
    finally:
        # 清理队列模式
        try:
            print(f"🔧 正在关闭队列模式...")
            nrc.queue_motion_set_status(socket_fd, False)
            print(f"✅ 队列模式已关闭")
        except Exception as e:
            print(f"⚠️ 关闭队列模式时发生错误: {e}")

def move_to_position_and_wait(socket_fd, target_pos, description="目标位置", timeout=30):
    """移动到指定位置并等待完成"""
    print(f"🎯 移动到{description}...")
    print(f"   目标: X={target_pos[0]:.1f}, Y={target_pos[1]:.1f}, Z={target_pos[2]:.1f}")
    
    # 创建运动命令
    move_cmd = nrc.MoveCmd()
    move_cmd.targetPosType = 0
    move_cmd.targetPosValue = nrc.VectorDouble()
    
    for val in target_pos:
        move_cmd.targetPosValue.append(val)
    
    move_cmd.coord = 3  # 用户坐标系
    move_cmd.userNum = USER_COORD_NUMBER
    move_cmd.velocity = VELOCITY_PERCENT
    move_cmd.acc = ACCEL_PERCENT
    move_cmd.dec = ACCEL_PERCENT
    move_cmd.pl = SMOOTHING_LEVEL
    
    # 执行移动
    result = nrc.robot_movel(socket_fd, move_cmd)
    if result != 0:
        print(f"❌ 移动失败，错误码: {result}")
        return False
    
    # 等待移动完成
    print(f"⏳ 等待移动完成...")
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        current_pos = get_current_position(socket_fd)
        if current_pos:
            distance = math.sqrt(
                (current_pos[0] - target_pos[0])**2 + 
                (current_pos[1] - target_pos[1])**2 + 
                (current_pos[2] - target_pos[2])**2
            )
            
            # 如果距离小于1mm，认为到达
            if distance < 1.0:
                print(f"✅ 移动完成，距离目标: {distance:.1f}mm")
                return True
        
        time.sleep(0.1)
    
    print(f"⚠️ 移动超时，但继续执行")
    return True

def execute_gcode_smart():
    """智能执行G-code"""

    print("=" * 60)
    print("INEXBOT机械臂 G-code 队列执行程序 - 修复版")
    print(f"测试前 {MAX_POINTS} 个指令")
    print("=" * 60)

    # 1. 解析G-code文件
    print(f"📄 正在解析G-code文件: {GCODE_FILE}")
    gcode_path = parse_gcode_file(GCODE_FILE, MAX_POINTS)
    if not gcode_path:
        return

    print(f"✅ 解析完成，共找到 {len(gcode_path)} 个路径点。")

    # 显示G-code范围
    x_coords = [p['x'] for p in gcode_path]
    y_coords = [p['y'] for p in gcode_path]
    z_coords = [p['z'] for p in gcode_path]

    print(f"📊 G-code坐标范围:")
    print(f"   X: {min(x_coords):.1f} ~ {max(x_coords):.1f}")
    print(f"   Y: {min(y_coords):.1f} ~ {max(y_coords):.1f}")
    print(f"   Z: {min(z_coords):.1f} ~ {max(z_coords):.1f}")

    # 2. 连接机械臂
    print(f"\n🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
    socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
    if socket_fd <= 0:
        print(f"❌ 连接失败，Socket ID: {socket_fd}")
        return

    print(f"✅ 连接成功！Socket ID: {socket_fd}")

    try:
        # 3. 机器人上电
        print(f"\nℹ️ 机器人需要上电，开始上电流程...")
        nrc.clear_error(socket_fd)
        time.sleep(0.2)
        result = nrc.set_servo_poweron(socket_fd)
        if result != 0:
            print(f"❌ 机器人上电失败，错误码: {result}")
            return
        time.sleep(1.5)
        print(f"✅ 机器人上电成功！")

        # 4. 设置用户坐标系
        print(f"ℹ️ 设置当前用户坐标系为: {USER_COORD_NUMBER}")
        result = nrc.set_user_coord_number(socket_fd, USER_COORD_NUMBER)
        if result != 0:
            print(f"❌ 设置用户坐标系失败，错误码: {result}")
            return

        # 5. 获取当前位置
        print(f"\n🔍 获取当前机械臂位置...")
        current_pos = get_current_position(socket_fd)
        if not current_pos:
            print(f"❌ 无法获取当前位置")
            return

        print(f"📊 当前位置: X={current_pos[0]:.1f}, Y={current_pos[1]:.1f}, Z={current_pos[2]:.1f}")
        print(f"         姿态: RX={math.degrees(current_pos[3]):.1f}°, RY={math.degrees(current_pos[4]):.1f}°, RZ={math.degrees(current_pos[5]):.1f}°")

        # 6. 移动到起始位置
        first_point = gcode_path[0]

        # 计算起始位置上方50mm
        gcode_a = first_point['a'] if first_point['a'] is not None else GCODE_DEFAULT_A
        gcode_b = first_point['b'] if first_point['b'] is not None else GCODE_DEFAULT_B
        gcode_c = first_point['c'] if first_point['c'] is not None else GCODE_DEFAULT_C

        rx_deg = gcode_a + GCODE_TO_ROBOT_OFFSET_A
        ry_deg = gcode_b + GCODE_TO_ROBOT_OFFSET_B
        rz_deg = gcode_c + GCODE_TO_ROBOT_OFFSET_C

        safe_pos = [
            first_point['x'], first_point['y'], first_point['z'] + 50.0,
            math.radians(rx_deg), math.radians(ry_deg), math.radians(rz_deg)
        ]

        print(f"🎯 移动到G-code起始位置上方50mm...")
        print(f"   目标: X={safe_pos[0]:.1f}, Y={safe_pos[1]:.1f}, Z={safe_pos[2]:.1f}")
        print(f"   姿态: RX={rx_deg:.1f}°, RY={ry_deg:.1f}°, RZ={rz_deg:.1f}°")

        if not move_to_position_and_wait(socket_fd, safe_pos, "起始位置上方"):
            print(f"❌ 移动到起始位置失败")
            return

        # 移动到第一个点
        first_target_pos = [
            first_point['x'], first_point['y'], first_point['z'],
            math.radians(rx_deg), math.radians(ry_deg), math.radians(rz_deg)
        ]

        print(f"🎯 移动到G-code第一个点...")
        print(f"   目标: X={first_target_pos[0]:.1f}, Y={first_target_pos[1]:.1f}, Z={first_target_pos[2]:.1f}")
        print(f"   姿态: RX={rx_deg:.1f}°, RY={ry_deg:.1f}°, RZ={rz_deg:.1f}°")

        if not move_to_position_and_wait(socket_fd, first_target_pos, "第一个点"):
            print(f"❌ 移动到第一个点失败")
            return

        print(f"\n🎉 成功到达G-code起始位置！")
        print(f"💡 现在开始执行G-code路径...")

        # 7. 执行剩余的G-code路径（使用队列模式）
        remaining_points = gcode_path[1:]  # 跳过第一个点，因为已经到达
        if remaining_points:
            success = execute_queue_motion(socket_fd, remaining_points)
            if success:
                print(f"\n🎉 G-code路径执行完成！")
            else:
                print(f"\n❌ G-code路径执行失败")
        else:
            print(f"\n✅ 只有一个点，无需队列执行")

    finally:
        # 清理和断开连接
        print(f"\nℹ️ 正在安全下电...")
        nrc.set_servo_poweroff(socket_fd)
        time.sleep(1)
        print(f"✅ 机器人已下电。")

        print(f"🔌 正在断开连接...")
        nrc.disconnect_robot(socket_fd)
        print(f"✅ 连接已断开。")

if __name__ == "__main__":
    execute_gcode_smart()
