#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 安全测试22个G-code点
使用更高的Z坐标和安全的角度设置
"""

import sys
import os
import time
import math

# 添加lib目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))
import nrc_interface as nrc

# 配置参数
ROBOT_IP = "************"
ROBOT_PORT = 6001

# 安全参数
SAFE_Z_OFFSET = 20.0  # 将Z坐标提高20mm以确保安全
SAFE_VELOCITY = 20.0  # 较慢的安全速度
SAFE_ACCEL = 20.0

# 角度设置（使用更保守的角度）
SAFE_RX = 180.0  # 度
SAFE_RY = 0.0    # 度  
SAFE_RZ = -90.0  # 度

# 全局变量
Size = 0

# 22个G-code点（从用户选择的内容）
GCODE_POINTS = [
    [104.6171, 207.5477, 4.9470],
    [105.2239, 206.4880, 4.9470],
    [105.6430, 205.3409, 4.9470],
    [105.8621, 204.1395, 4.9470],
    [105.8751, 202.9184, 4.9470],
    [105.6815, 201.7126, 4.9470],
    [105.2869, 200.5569, 4.9470],
    [104.7026, 199.4845, 4.9470],
    [103.9455, 198.5263, 4.9470],
    [103.0374, 197.7099, 4.9470],
    [102.0043, 197.0585, 4.9470],
    [100.8762, 196.5912, 4.9470],
    [99.6851, 196.3213, 4.9470],
    [98.4656, 196.2566, 4.9470],
    [97.2527, 196.3988, 4.9470],
    [96.0814, 196.7440, 4.9470],
    [94.9852, 197.2823, 4.9470],
    [93.9957, 197.9980, 4.9470],
    [93.1413, 198.8706, 4.9470],
    [92.4468, 199.8750, 4.9470],
    [91.9321, 200.9825, 4.9470],
    [91.6118, 202.1609, 4.9470],
]

def connect(ip, port):
    fd = nrc.connect_robot(ip, port)
    print("初始化控制器ID: ", fd)
    return fd

def clear_error(socketFd):
    nrc.clear_error(socketFd)
    print('清错')

def power_on(socketFd):
    nrc.set_servo_poweron(socketFd)
    print('上使能')

def create_relative_move_cmd(current_pos, gcode_point, scale_factor=0.1):
    """创建基于当前位置的相对移动命令"""
    # 将G-code坐标缩放并转换为相对于当前位置的移动
    gcode_x, gcode_y, gcode_z = gcode_point

    # 计算相对移动（缩放到10%以确保安全）
    relative_x = (gcode_x - GCODE_POINTS[0][0]) * scale_factor
    relative_y = (gcode_y - GCODE_POINTS[0][1]) * scale_factor
    relative_z = 5.0  # 固定在当前位置上方5mm

    # 计算目标位置
    target_x = current_pos[0] + relative_x
    target_y = current_pos[1] + relative_y
    target_z = current_pos[2] + relative_z

    # 保持当前姿态
    target_rx = current_pos[3]
    target_ry = current_pos[4]
    target_rz = current_pos[5]

    # 创建位置向量
    pos = nrc.VectorDouble()
    target_pos = [target_x, target_y, target_z, target_rx, target_ry, target_rz]

    for value in target_pos:
        pos.append(value)

    # 创建运动命令
    cmd = nrc.MoveCmd()
    cmd.targetPosType = nrc.PosType_data
    cmd.targetPosValue = pos
    cmd.velocity = SAFE_VELOCITY
    cmd.acc = SAFE_ACCEL
    cmd.dec = SAFE_ACCEL

    return cmd, target_pos

def get_current_cartesian_position(socketFd):
    """获取当前笛卡尔位置"""
    try:
        pos = nrc.VectorDouble()
        coord = 1  # 获取笛卡尔坐标
        result = nrc.get_current_position(socketFd, coord, pos)
        if result == 0 and len(pos) >= 6:
            return [pos[i] for i in range(6)]
        else:
            print(f"获取位置失败，结果: {result}, 长度: {len(pos)}")
            return None
    except Exception as e:
        print(f"❌ 获取当前位置时发生错误: {e}")
        return None

def test_22_points_queue(socketFd):
    """测试22个G-code点的队列执行 - 基于当前位置的相对移动"""
    global Size
    Size = 0

    print(f"\n🚀 基于当前位置的相对移动测试...")

    # 0. 获取当前位置作为基准
    print("📍 获取当前机器人位置...")
    current_pos = get_current_cartesian_position(socketFd)
    if current_pos is None:
        print("❌ 无法获取当前位置")
        return False

    print(f"📊 当前位置: X={current_pos[0]:.1f}, Y={current_pos[1]:.1f}, Z={current_pos[2]:.1f}")
    print(f"📊 当前姿态: RX={math.degrees(current_pos[3]):.1f}°, RY={math.degrees(current_pos[4]):.1f}°, RZ={math.degrees(current_pos[5]):.1f}°")

    # 计算G-code路径的中心点
    gcode_x_coords = [p[0] for p in GCODE_POINTS]
    gcode_y_coords = [p[1] for p in GCODE_POINTS]
    gcode_center_x = (min(gcode_x_coords) + max(gcode_x_coords)) / 2
    gcode_center_y = (min(gcode_y_coords) + max(gcode_y_coords)) / 2

    print(f"📊 G-code路径中心: X={gcode_center_x:.1f}, Y={gcode_center_y:.1f}")
    print(f"📊 G-code路径范围: X={min(gcode_x_coords):.1f}~{max(gcode_x_coords):.1f}, Y={min(gcode_y_coords):.1f}~{max(gcode_y_coords):.1f}")

    # 1. 启动队列模式
    print("\n=== 启动队列模式 ===")
    result = nrc.queue_motion_set_status(socketFd, True)
    print(f"启动队列模式结果: {result}")
    if result != 0:
        return False
    time.sleep(1)
    
    # 2. 添加22个点（分两批，每批11个）
    batch_size = 11
    
    for batch_num in range(2):
        start_idx = batch_num * batch_size
        end_idx = min(start_idx + batch_size, len(GCODE_POINTS))
        batch_points = GCODE_POINTS[start_idx:end_idx]
        
        print(f"\n=== 处理第 {batch_num + 1} 批：点 {start_idx + 1}-{end_idx} ({len(batch_points)} 个点) ===")
        
        # 清除队列（为新批次准备）
        if batch_num > 0:
            result = nrc.queue_motion_clear_Data(socketFd)
            if result != 0:
                print(f"❌ 清除队列失败，错误码: {result}")
                return False
        
        # 添加当前批次的点
        batch_size_counter = 0
        for i, point in enumerate(batch_points):
            cmd, target_pos = create_relative_move_cmd(current_pos, point)

            print(f"添加点 {start_idx + i + 1}: X={target_pos[0]:.1f}, Y={target_pos[1]:.1f}, Z={target_pos[2]:.1f}")
            print(f"   相对移动: ΔX={(target_pos[0]-current_pos[0]):.1f}, ΔY={(target_pos[1]-current_pos[1]):.1f}")

            result = nrc.queue_motion_push_back_moveL(socketFd, cmd)
            if result == 0:
                batch_size_counter += 1
                print(f"✅ 成功添加，当前批次Size: {batch_size_counter}")
            else:
                print(f"❌ 指令插入失败，错误码: {result}")
                return False
        
        # 发送当前批次并执行
        print(f"\n=== 发送第 {batch_num + 1} 批队列并执行 ===")
        result = nrc.queue_motion_send_to_controller(socketFd, batch_size_counter)
        print(f"运动队列长度: {batch_size_counter}")
        print(f"发送队列结果: {result}")
        if result != 0:
            print(f"❌ 发送队列失败")
            return False
        
        time.sleep(0.5)
        
        # 检查队列长度
        queue_len = 0
        result = nrc.queue_motion_get_queuelen(socketFd, queue_len)
        if isinstance(result, list) and len(result) > 1:
            queue_len = result[1]
        print(f"发送后队列长度: {queue_len}")
        
        if queue_len == 0:
            print(f"❌ 警告：第 {batch_num + 1} 批队列长度为0")
            return False
        
        # 等待当前批次执行完成
        print(f"⏳ 等待第 {batch_num + 1} 批执行完成...")
        jug = 1
        while jug != 0:
            status = 1
            status = nrc.get_robot_running_state(socketFd, status) 
            jug = status[1]
            if jug == 2:
                print(f"   🔄 机器人正在运动...")
            elif jug == 0:
                print(f"   ✅ 第 {batch_num + 1} 批执行完成")
            time.sleep(0.5)
    
    print(f"\n🎉 22个G-code点安全测试完成！")
    return True

def main():
    print("=" * 60)
    print("INEXBOT机械臂 安全测试22个G-code点")
    print(f"原始Z={GCODE_POINTS[0][2]}mm -> 安全Z={GCODE_POINTS[0][2] + SAFE_Z_OFFSET}mm")
    print("=" * 60)
    
    # 1. 连接
    socketFd = connect(ROBOT_IP, str(ROBOT_PORT))
    if socketFd <= 0:
        print(f"❌ 连接失败，Socket ID: {socketFd}")
        return
    
    try:
        # 2. 清错和上电
        clear_error(socketFd)
        time.sleep(0.5)
        power_on(socketFd)
        time.sleep(2)
        
        # 3. 设置远程模式
        print("设置远程模式...")
        result = nrc.set_current_mode(socketFd, 1)  # 1 = 远程模式
        print(f"设置远程模式结果: {result}")
        time.sleep(0.5)
        
        # 4. 执行22点测试
        success = test_22_points_queue(socketFd)
        if success:
            print(f"\n🎉 22点队列测试成功！")
        else:
            print(f"\n❌ 22点队列测试失败")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        
    finally:
        # 清理
        try:
            print("\n=== 清理队列模式 ===")
            nrc.queue_motion_set_status(socketFd, False)
            nrc.set_current_mode(socketFd, 0)  # 0 = 示教模式
            print("队列模式已关闭")
        except Exception as e:
            print(f"清理时发生错误: {e}")
        
        # 下电和断开
        print("\n=== 下电和断开 ===")
        nrc.set_servo_poweroff(socketFd)
        time.sleep(1)
        nrc.disconnect_robot(socketFd)
        print("已断开连接")

if __name__ == "__main__":
    main()
