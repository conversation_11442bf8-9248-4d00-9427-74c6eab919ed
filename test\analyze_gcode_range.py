#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析G-code文件的坐标范围
"""

import sys
import os
import re
import math

def parse_gcode_file(filename):
    """解析G-code文件，提取路径点"""
    path_points = []
    
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                line = line.strip()
                if not line or line.startswith(';'):
                    continue
                
                # 匹配G1指令
                if line.startswith('G1'):
                    # 提取坐标值
                    x = re.search(r'X([-+]?\d*\.?\d+)', line)
                    y = re.search(r'Y([-+]?\d*\.?\d+)', line)
                    z = re.search(r'Z([-+]?\d*\.?\d+)', line)
                    a = re.search(r'A([-+]?\d*\.?\d+)', line)
                    b = re.search(r'B([-+]?\d*\.?\d+)', line)
                    c = re.search(r'C([-+]?\d*\.?\d+)', line)
                    
                    if x and y and z:
                        point = {
                            'x': float(x.group(1)),
                            'y': float(y.group(1)),
                            'z': float(z.group(1)),
                            'a': float(a.group(1)) if a else None,
                            'b': float(b.group(1)) if b else None,
                            'c': float(c.group(1)) if c else None,
                            'line': line_num
                        }
                        path_points.append(point)
                            
    except FileNotFoundError:
        print(f"❌ 找不到G-code文件: {filename}")
        return None
    except Exception as e:
        print(f"❌ 解析G-code文件时发生错误: {e}")
        return None
    
    return path_points

def analyze_gcode_range(gcode_points):
    """分析G-code坐标范围"""
    if not gcode_points:
        return
    
    print("=" * 60)
    print("G-code坐标范围分析")
    print("=" * 60)
    
    # 提取所有坐标
    x_coords = [p['x'] for p in gcode_points]
    y_coords = [p['y'] for p in gcode_points]
    z_coords = [p['z'] for p in gcode_points]
    
    a_coords = [p['a'] for p in gcode_points if p['a'] is not None]
    b_coords = [p['b'] for p in gcode_points if p['b'] is not None]
    c_coords = [p['c'] for p in gcode_points if p['c'] is not None]
    
    print(f"📊 总共 {len(gcode_points)} 个G-code点")
    print()
    
    # 位置坐标范围
    print("📍 位置坐标范围 (mm):")
    print(f"   X: {min(x_coords):.1f} ~ {max(x_coords):.1f} (范围: {max(x_coords) - min(x_coords):.1f})")
    print(f"   Y: {min(y_coords):.1f} ~ {max(y_coords):.1f} (范围: {max(y_coords) - min(y_coords):.1f})")
    print(f"   Z: {min(z_coords):.1f} ~ {max(z_coords):.1f} (范围: {max(z_coords) - min(z_coords):.1f})")
    print()
    
    # 角度坐标范围
    if a_coords:
        print("🔄 角度坐标范围 (度):")
        print(f"   A: {min(a_coords):.1f} ~ {max(a_coords):.1f} (范围: {max(a_coords) - min(a_coords):.1f})")
        if b_coords:
            print(f"   B: {min(b_coords):.1f} ~ {max(b_coords):.1f} (范围: {max(b_coords) - min(b_coords):.1f})")
        if c_coords:
            print(f"   C: {min(c_coords):.1f} ~ {max(c_coords):.1f} (范围: {max(c_coords) - min(c_coords):.1f})")
    else:
        print("🔄 角度坐标: 未指定 (将使用默认值)")
    print()
    
    # 转换后的机器人角度范围
    GCODE_DEFAULT_A = 0.0
    GCODE_DEFAULT_B = 0.0
    GCODE_DEFAULT_C = 0.0
    
    GCODE_TO_ROBOT_OFFSET_A = 180.0
    GCODE_TO_ROBOT_OFFSET_B = 0.0
    GCODE_TO_ROBOT_OFFSET_C = -90.0
    
    print("🤖 转换后的机器人角度范围 (度):")
    
    if a_coords:
        robot_rx_min = min(a_coords) + GCODE_TO_ROBOT_OFFSET_A
        robot_rx_max = max(a_coords) + GCODE_TO_ROBOT_OFFSET_A
    else:
        robot_rx_min = robot_rx_max = GCODE_DEFAULT_A + GCODE_TO_ROBOT_OFFSET_A
    
    if b_coords:
        robot_ry_min = min(b_coords) + GCODE_TO_ROBOT_OFFSET_B
        robot_ry_max = max(b_coords) + GCODE_TO_ROBOT_OFFSET_B
    else:
        robot_ry_min = robot_ry_max = GCODE_DEFAULT_B + GCODE_TO_ROBOT_OFFSET_B
    
    if c_coords:
        robot_rz_min = min(c_coords) + GCODE_TO_ROBOT_OFFSET_C
        robot_rz_max = max(c_coords) + GCODE_TO_ROBOT_OFFSET_C
    else:
        robot_rz_min = robot_rz_max = GCODE_DEFAULT_C + GCODE_TO_ROBOT_OFFSET_C
    
    print(f"   RX: {robot_rx_min:.1f} ~ {robot_rx_max:.1f}")
    print(f"   RY: {robot_ry_min:.1f} ~ {robot_ry_max:.1f}")
    print(f"   RZ: {robot_rz_min:.1f} ~ {robot_rz_max:.1f}")
    print()
    
    # 检查是否有异常值
    print("⚠️ 潜在问题检查:")
    
    # 检查Z坐标是否过低
    if min(z_coords) < 0:
        print(f"   ❌ Z坐标有负值: {min(z_coords):.1f}mm (可能导致碰撞)")
    elif min(z_coords) < 5:
        print(f"   ⚠️ Z坐标过低: {min(z_coords):.1f}mm (建议 > 5mm)")
    else:
        print(f"   ✅ Z坐标安全: 最低 {min(z_coords):.1f}mm")
    
    # 检查工作空间
    max_reach = math.sqrt(max(x_coords)**2 + max(y_coords)**2)
    print(f"   📏 最大工作半径: {max_reach:.1f}mm")
    
    if max_reach > 800:  # 假设机器人最大工作半径约800mm
        print(f"   ❌ 可能超出工作空间 (建议 < 800mm)")
    else:
        print(f"   ✅ 工作半径合理")
    
    # 显示前几个点的详细信息
    print()
    print("📋 前5个G-code点详情:")
    for i, point in enumerate(gcode_points[:5]):
        gcode_a = point['a'] if point['a'] is not None else GCODE_DEFAULT_A
        gcode_b = point['b'] if point['b'] is not None else GCODE_DEFAULT_B
        gcode_c = point['c'] if point['c'] is not None else GCODE_DEFAULT_C
        
        robot_rx = gcode_a + GCODE_TO_ROBOT_OFFSET_A
        robot_ry = gcode_b + GCODE_TO_ROBOT_OFFSET_B
        robot_rz = gcode_c + GCODE_TO_ROBOT_OFFSET_C
        
        print(f"   点{i+1}: X={point['x']:.1f}, Y={point['y']:.1f}, Z={point['z']:.1f}")
        print(f"        机器人角度: RX={robot_rx:.1f}°, RY={robot_ry:.1f}°, RZ={robot_rz:.1f}°")

def main():
    gcode_file = "jiyi.Gcode"
    
    print(f"📄 正在分析G-code文件: {gcode_file}")
    gcode_points = parse_gcode_file(gcode_file)
    
    if gcode_points:
        analyze_gcode_range(gcode_points)
    else:
        print("❌ 无法分析G-code文件")

if __name__ == "__main__":
    main()
