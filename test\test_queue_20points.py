#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 队列模式测试 - 20个点限制测试
基于官方文档：一次最多插入20条指令
"""

import sys
import os
import time
import math
import re

# 添加lib目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))
import nrc_interface as nrc

# 配置参数
ROBOT_IP = "************"
ROBOT_PORT = 6001
GCODE_FILE = "jiyi.Gcode"
MAX_POINTS = 20  # 测试20个点

# 运动参数
USER_COORD_NUMBER = 1
VELOCITY_PERCENT = 30.0
ACCEL_PERCENT = 30.0
SMOOTHING_LEVEL = 0

# G-code角度默认值和偏移
GCODE_DEFAULT_A = 0.0
GCODE_DEFAULT_B = 0.0
GCODE_DEFAULT_C = 0.0

GCODE_TO_ROBOT_OFFSET_A = 180.0
GCODE_TO_ROBOT_OFFSET_B = 0.0
GCODE_TO_ROBOT_OFFSET_C = -90.0

def parse_gcode_file(filename, max_points=None):
    """解析G-code文件，提取路径点"""
    path_points = []
    
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            for line_num, line in enumerate(file, 1):
                line = line.strip()
                if not line or line.startswith(';'):
                    continue
                
                # 匹配G1指令
                if line.startswith('G1'):
                    # 提取坐标值
                    x = re.search(r'X([-+]?\d*\.?\d+)', line)
                    y = re.search(r'Y([-+]?\d*\.?\d+)', line)
                    z = re.search(r'Z([-+]?\d*\.?\d+)', line)
                    a = re.search(r'A([-+]?\d*\.?\d+)', line)
                    b = re.search(r'B([-+]?\d*\.?\d+)', line)
                    c = re.search(r'C([-+]?\d*\.?\d+)', line)
                    
                    if x and y and z:
                        point = {
                            'x': float(x.group(1)),
                            'y': float(y.group(1)),
                            'z': float(z.group(1)),
                            'a': float(a.group(1)) if a else None,
                            'b': float(b.group(1)) if b else None,
                            'c': float(c.group(1)) if c else None,
                            'line': line_num
                        }
                        path_points.append(point)
                        
                        if max_points and len(path_points) >= max_points:
                            break
                            
    except FileNotFoundError:
        print(f"❌ 找不到G-code文件: {filename}")
        return None
    except Exception as e:
        print(f"❌ 解析G-code文件时发生错误: {e}")
        return None
    
    return path_points

def get_current_position(socket_fd):
    """获取当前位置（用户坐标系）"""
    try:
        pos = nrc.VectorDouble()
        for _ in range(7):
            pos.append(0.0)
        
        result = nrc.get_current_position(socket_fd, 3, pos)  # 3 = 用户坐标系
        if result == 0 and len(pos) >= 6:
            return [pos[i] for i in range(6)]
        else:
            return None
    except Exception as e:
        print(f"❌ 获取当前位置时发生错误: {e}")
        return None

def test_queue_motion_20points(socket_fd, gcode_points):
    """测试队列运动（最多20个点）"""
    try:
        print(f"\n🚀 测试队列模式执行 {len(gcode_points)} 个G-code点...")
        
        # 1. 设置远程模式
        print(f"🎮 设置机器人为远程模式...")
        result = nrc.set_current_mode(socket_fd, 1)  # 1 = 远程模式
        if result != 0:
            print(f"❌ 设置远程模式失败，错误码: {result}")
            return False
        time.sleep(0.5)
        print(f"✅ 机器人已设置为远程模式")
        
        # 2. 启用队列模式
        print(f"🔧 启用队列模式...")
        result = nrc.queue_motion_set_status(socket_fd, True)
        if result != 0:
            print(f"❌ 启用队列模式失败，错误码: {result}")
            return False
        time.sleep(1)
        print(f"✅ 队列模式已启用")
        
        # 3. 清除队列数据
        result = nrc.queue_motion_clear_Data(socket_fd)
        if result != 0:
            print(f"❌ 清除队列数据失败，错误码: {result}")
            return False
        
        # 4. 添加运动指令到队列（最多20个）
        print(f"📝 添加 {len(gcode_points)} 个指令到队列...")
        points_added = 0
        
        for i, point in enumerate(gcode_points):
            if i >= 20:  # 严格限制20个
                print(f"⚠️ 达到20个指令限制，停止添加")
                break
                
            # 计算角度
            gcode_a = point['a'] if point['a'] is not None else GCODE_DEFAULT_A
            gcode_b = point['b'] if point['b'] is not None else GCODE_DEFAULT_B
            gcode_c = point['c'] if point['c'] is not None else GCODE_DEFAULT_C

            rx_deg = gcode_a + GCODE_TO_ROBOT_OFFSET_A
            ry_deg = gcode_b + GCODE_TO_ROBOT_OFFSET_B
            rz_deg = gcode_c + GCODE_TO_ROBOT_OFFSET_C

            # 创建运动命令（按照官方demo方式）
            move_cmd = nrc.MoveCmd()
            move_cmd.targetPosType = nrc.PosType_data  # 使用枚举常量
            move_cmd.targetPosValue = nrc.VectorDouble()

            # 设置目标位置
            target_pos = [
                point['x'], point['y'], point['z'],
                math.radians(rx_deg), math.radians(ry_deg), math.radians(rz_deg)
            ]

            for val in target_pos:
                move_cmd.targetPosValue.append(val)

            # 按照官方demo，不设置coord和userNum
            move_cmd.velocity = VELOCITY_PERCENT
            move_cmd.acc = ACCEL_PERCENT
            move_cmd.dec = ACCEL_PERCENT

            # 添加到队列
            result = nrc.queue_motion_push_back_moveL(socket_fd, move_cmd)
            if result != 0:
                print(f"❌ 添加第 {i+1} 个指令到队列失败，错误码: {result}")
                break

            points_added += 1
            print(f"   ✅ 已添加第 {i+1} 个指令: X={point['x']:.1f}, Y={point['y']:.1f}, Z={point['z']:.1f}")

        print(f"✅ 成功添加了 {points_added} 个指令到队列")
        
        # 5. 检查队列长度
        print(f"🔍 检查队列状态...")
        queue_len = 0
        result = nrc.queue_motion_get_queuelen(socket_fd, queue_len)
        if isinstance(result, list) and len(result) > 1:
            queue_len = result[1]
        print(f"📊 当前队列长度: {queue_len}")
        
        if queue_len == 0:
            print(f"❌ 警告：队列长度为0，指令可能没有正确添加")
            return False
        
        # 6. 发送队列到控制器
        print(f"🚀 发送队列到控制器...")
        result = nrc.queue_motion_send_to_controller(socket_fd, points_added)
        if result != 0:
            print(f"❌ 发送队列到控制器失败，错误码: {result}")
            return False
        
        print(f"✅ 队列已发送到控制器！")
        
        # 7. 再次检查队列长度
        time.sleep(0.5)
        result = nrc.queue_motion_get_queuelen(socket_fd, queue_len)
        if isinstance(result, list) and len(result) > 1:
            queue_len = result[1]
        print(f"📊 发送后队列长度: {queue_len}")
        
        print(f"🎯 开始执行队列运动...")

        # 8. 监控队列执行（按照官方demo方式）
        start_time = time.time()
        print(f"📊 运动队列长度: {points_added}")
        time.sleep(0.5)

        # 使用官方demo的监控方式
        jug = 1
        while jug != 0:
            # status的类型是一个int类型，获取到状态之后status会变成一个列表，所以再次调用接口的时候需要重新定义status为int类型传入
            status = 1
            result = nrc.get_robot_running_state(socket_fd, status)
            if isinstance(result, list) and len(result) > 1:
                jug = result[1]
                if jug == 2:  # 2表示正在运动
                    print(f"   🔄 机器人正在运动...")
                elif jug == 0:  # 0表示停止
                    print(f"   ✅ 机器人运动完成")
            time.sleep(0.5)

        elapsed = time.time() - start_time
        print(f"🎉 队列运动执行完成！总用时: {elapsed:.1f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ 队列模式执行时发生错误: {e}")
        return False
    
    finally:
        # 清理队列模式
        try:
            print(f"🔧 正在关闭队列模式...")
            nrc.queue_motion_set_status(socket_fd, False)
            
            print(f"🎮 切换回示教模式...")
            nrc.set_current_mode(socket_fd, 0)  # 0 = 示教模式
            
            print(f"✅ 队列模式已关闭，已切换回示教模式")
        except Exception as e:
            print(f"⚠️ 关闭队列模式时发生错误: {e}")

def main():
    print("=" * 60)
    print("INEXBOT机械臂 队列模式测试 - 20个点限制")
    print(f"测试前 {MAX_POINTS} 个指令")
    print("=" * 60)
    
    # 1. 解析G-code文件
    print(f"📄 正在解析G-code文件: {GCODE_FILE}")
    gcode_path = parse_gcode_file(GCODE_FILE, MAX_POINTS)
    if not gcode_path:
        return
    
    print(f"✅ 解析完成，共找到 {len(gcode_path)} 个路径点。")
    
    # 2. 连接机械臂
    print(f"\n🔗 正在连接机械臂 {ROBOT_IP}:{ROBOT_PORT}...")
    socket_fd = nrc.connect_robot(ROBOT_IP, str(ROBOT_PORT))
    if socket_fd <= 0:
        print(f"❌ 连接失败，Socket ID: {socket_fd}")
        return
    
    print(f"✅ 连接成功！Socket ID: {socket_fd}")
    
    try:
        # 3. 机器人上电
        print(f"\nℹ️ 机器人需要上电，开始上电流程...")
        nrc.clear_error(socket_fd)
        time.sleep(0.2)
        result = nrc.set_servo_poweron(socket_fd)
        if result != 0:
            print(f"❌ 机器人上电失败，错误码: {result}")
            return
        time.sleep(1.5)
        print(f"✅ 机器人上电成功！")
        
        # 4. 设置用户坐标系
        print(f"ℹ️ 设置当前用户坐标系为: {USER_COORD_NUMBER}")
        result = nrc.set_user_coord_number(socket_fd, USER_COORD_NUMBER)
        if result != 0:
            print(f"❌ 设置用户坐标系失败，错误码: {result}")
            return
        
        # 5. 测试队列运动
        success = test_queue_motion_20points(socket_fd, gcode_path)
        if success:
            print(f"\n🎉 队列运动测试成功！")
        else:
            print(f"\n❌ 队列运动测试失败")
        
    finally:
        # 清理和断开连接
        print(f"\nℹ️ 正在安全下电...")
        nrc.set_servo_poweroff(socket_fd)
        time.sleep(1)
        print(f"✅ 机器人已下电。")
        
        print(f"🔌 正在断开连接...")
        nrc.disconnect_robot(socket_fd)
        print(f"✅ 连接已断开。")

if __name__ == "__main__":
    main()
