#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INEXBOT机械臂 队列测试 - 基于当前位置的微小移动
"""

import sys
import os
import time
import math

# 添加lib目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'lib'))
import nrc_interface as nrc

# 配置参数
ROBOT_IP = "************"
ROBOT_PORT = 6001

# 全局变量
Size = 0

def connect(ip, port):
    fd = nrc.connect_robot(ip, port)
    print("初始化控制器ID: ", fd)
    return fd

def clear_error(socketFd):
    nrc.clear_error(socketFd)
    print('清错')

def power_on(socketFd):
    nrc.set_servo_poweron(socketFd)
    print('上使能')

def get_current_cartesian_position(socketFd):
    """获取当前笛卡尔位置"""
    try:
        pos = nrc.VectorDouble()
        coord = 1  # 获取笛卡尔坐标
        result = nrc.get_current_position(socketFd, coord, pos)
        if result == 0 and len(pos) >= 6:
            return [pos[i] for i in range(6)]
        else:
            print(f"获取位置失败，结果: {result}, 长度: {len(pos)}")
            return None
    except Exception as e:
        print(f"❌ 获取当前位置时发生错误: {e}")
        return None

def create_move_cmd_from_pos(target_pos):
    """基于位置数组创建运动命令"""
    pos = nrc.VectorDouble()
    for value in target_pos:
        pos.append(value)

    cmd = nrc.MoveCmd()
    cmd.targetPosType = nrc.PosType_data
    cmd.targetPosValue = pos
    cmd.velocity = 30
    cmd.acc = 30
    cmd.dec = 30
    
    return cmd

def test_queue_with_current_pos(socketFd):
    """基于当前位置的队列测试"""
    global Size
    Size = 0
    
    print(f"\n🚀 基于当前位置的队列测试...")
    
    # 1. 获取当前位置
    print("📍 获取当前笛卡尔位置...")
    current_pos = get_current_cartesian_position(socketFd)
    if current_pos is None:
        print("❌ 无法获取当前位置")
        return False
    
    print(f"📊 当前位置: X={current_pos[0]:.3f}, Y={current_pos[1]:.3f}, Z={current_pos[2]:.3f}")
    print(f"📊 当前姿态: RX={math.degrees(current_pos[3]):.1f}°, RY={math.degrees(current_pos[4]):.1f}°, RZ={math.degrees(current_pos[5]):.1f}°")
    
    # 2. 启动队列模式
    print("\n=== 启动队列模式 ===")
    result = nrc.queue_motion_set_status(socketFd, True)
    print(f"启动队列模式结果: {result}")
    if result != 0:
        return False
    time.sleep(1)
    
    # 3. 添加基于当前位置的微小移动
    print("\n=== 添加微小移动指令 ===")
    
    # 创建3个微小的移动：Z轴上下移动
    movements = [
        [current_pos[0], current_pos[1], current_pos[2] + 5.0, current_pos[3], current_pos[4], current_pos[5]],   # Z+5mm
        [current_pos[0], current_pos[1], current_pos[2], current_pos[3], current_pos[4], current_pos[5]],         # 回到原位
        [current_pos[0], current_pos[1], current_pos[2] + 5.0, current_pos[3], current_pos[4], current_pos[5]],   # Z+5mm
    ]
    
    for i, target_pos in enumerate(movements):
        cmd = create_move_cmd_from_pos(target_pos)
        print(f"添加第 {i+1} 个指令: Z={target_pos[2]:.1f}")
        
        result = nrc.queue_motion_push_back_moveL(socketFd, cmd)
        if result == 0:
            Size += 1
            print(f"✅ 成功添加，当前Size: {Size}")
        else:
            print(f"❌ 指令插入失败，错误码: {result}")
            return False
    
    # 4. 发送队列并执行
    print(f"\n=== 发送队列并执行 ===")
    result = nrc.queue_motion_send_to_controller(socketFd, Size)
    print(f"运动队列长度: {Size}")
    print(f"发送队列结果: {result}")
    if result != 0:
        print(f"❌ 发送队列失败")
        return False
    
    time.sleep(0.5)
    
    # 5. 检查队列长度
    queue_len = 0
    result = nrc.queue_motion_get_queuelen(socketFd, queue_len)
    if isinstance(result, list) and len(result) > 1:
        queue_len = result[1]
    print(f"发送后队列长度: {queue_len}")
    
    if queue_len == 0:
        print(f"❌ 警告：队列长度为0")
        return False
    
    # 6. 等待执行完成
    print(f"⏳ 等待队列执行完成...")
    jug = 1
    while jug != 0:
        status = 1
        status = nrc.get_robot_running_state(socketFd, status) 
        jug = status[1]
        if jug == 2:
            print(f"   🔄 机器人正在运动...")
        elif jug == 0:
            print(f"   ✅ 队列执行完成")
        time.sleep(0.5)
    
    print(f"\n🎉 基于当前位置的队列测试成功！")
    return True

def main():
    print("=" * 60)
    print("INEXBOT机械臂 队列测试 - 基于当前位置的微小移动")
    print("=" * 60)
    
    # 1. 连接
    socketFd = connect(ROBOT_IP, str(ROBOT_PORT))
    if socketFd <= 0:
        print(f"❌ 连接失败，Socket ID: {socketFd}")
        return
    
    try:
        # 2. 清错和上电
        clear_error(socketFd)
        time.sleep(0.5)
        power_on(socketFd)
        time.sleep(2)
        
        # 3. 设置远程模式
        print("设置远程模式...")
        result = nrc.set_current_mode(socketFd, 1)  # 1 = 远程模式
        print(f"设置远程模式结果: {result}")
        time.sleep(0.5)
        
        # 4. 设置坐标系
        print("设置笛卡尔坐标系...")
        result = nrc.set_current_coord(socketFd, 1)  # 1 = 笛卡尔坐标
        print(f"设置坐标系结果: {result}")
        
        # 5. 执行队列测试
        success = test_queue_with_current_pos(socketFd)
        if success:
            print(f"\n🎉 队列测试成功！")
        else:
            print(f"\n❌ 队列测试失败")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        
    finally:
        # 清理
        try:
            print("\n=== 清理队列模式 ===")
            nrc.queue_motion_set_status(socketFd, False)
            nrc.set_current_mode(socketFd, 0)  # 0 = 示教模式
            print("队列模式已关闭")
        except Exception as e:
            print(f"清理时发生错误: {e}")
        
        # 下电和断开
        print("\n=== 下电和断开 ===")
        nrc.set_servo_poweroff(socketFd)
        time.sleep(1)
        nrc.disconnect_robot(socketFd)
        print("已断开连接")

if __name__ == "__main__":
    main()
