import sys
import os
import time
import math
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'lib'))

import nrc_interface as nrc
import time

def normalize_euler_angles(rx, ry, rz):
    """标准化欧拉角，处理万向锁情况"""
    # 将角度标准化到 [-π, π] 范围
    rx = ((rx + math.pi) % (2 * math.pi)) - math.pi
    ry = ((ry + math.pi) % (2 * math.pi)) - math.pi
    rz = ((rz + math.pi) % (2 * math.pi)) - math.pi
    
    # 检查是否接近万向锁位置
    if abs(abs(ry) - math.pi/2) < 0.01:  # 接近±90°
        # 固定一个角度为0，调整另一个角度
        if ry > 0:  # +90°情况
            total = rx + rz
            return (0.0, ry, total)
        else:  # -90°情况
            diff = rx - rz
            return (diff, ry, 0.0)
    
    return (rx, ry, rz)
socketFd = nrc.connect_robot("192.168.1.13", "6001")
coord = 3
pos = nrc.VectorDouble()
for i in range(7):
    pos.append(0.0)

result_code = nrc.get_current_position(socketFd, coord, pos)
if result_code == 0:
    # 标准化欧拉角
    rx_norm, ry_norm, rz_norm = normalize_euler_angles(pos[3], pos[4], pos[5])
    
    print(f"\n✅ 获取位置成功！")
    print(f"📍 当前位置: X={pos[0]:.3f}, Y={pos[1]:.3f}, Z={pos[2]:.3f}")
    print(f"📍 原始姿态: RX={math.degrees(pos[3]):.3f}°, RY={math.degrees(pos[4]):.3f}°, RZ={math.degrees(pos[5]):.3f}°")
    print(f"📍 标准姿态: RX={math.degrees(rx_norm):.3f}°, RY={math.degrees(ry_norm):.3f}°, RZ={math.degrees(rz_norm):.3f}°")
else:
    print(f"\n❌ 获取位置失败，错误代码: {result_code}")

nrc.disconnect_robot(socketFd)
print("\n🔗 连接已断开")